import { ReactNode, useState } from "react";
import { Link, useLocation } from "react-router-dom";
import { cn } from "@/lib/utils";
import {
  LayoutDashboard,
  Users,
  Calendar,
  Trophy,
  Shuffle,
  Handshake,
  Menu,
  Play,
  Info,
  ArrowLeft,
  MessageCircle,
  Coffee,
  UserCircle,
} from "lucide-react";
import { useTranslation } from "react-i18next";
import { Button } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>onte<PERSON>,
  Sheet<PERSON>eader,
  SheetTitle,
  SheetTrigger
} from "@/components/ui/sheet";
import ThemeToggle from "@/components/ThemeToggle";
import LanguageToggle from "@/components/LanguageToggle";
import LogoComponent from "@/components/LogoComponent";
import { useTheme } from "@/context/ThemeContext";
import { useMediaQuery } from "@/hooks/use-media-query";
import { HalftoneBackground } from "@/components/ui/halftone-background";
import {
  Toolt<PERSON>,
  Toolt<PERSON>Content,
  Toolt<PERSON>Provider,
  TooltipTrigger,
} from "@/components/ui/tooltip";

// Constants for external links
const FEEDBACK_FORM_URL = "https://docs.google.com/forms/d/e/1FAIpQLSdtZuMIn1UVQIqCdjC7cJOk7DYoqIMeYv-HtK9o-vPZ6UIrAg/viewform?usp=sharing";
const CAFECITO_URL = "https://cafecito.app/fulbitostats";

interface DemoLayoutProps {
  children: ReactNode;
  title?: string;
}

const DemoLayout = ({ children, title }: DemoLayoutProps) => {
  const location = useLocation();
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const { theme } = useTheme();
  const { t } = useTranslation();
  const isMobile = useMediaQuery("(max-width: 768px)");

  // Navigation links for demo - matching authenticated app structure
  // Leaderboard, chemistry, and team generator are dashboard sub-tabs, not main nav items
  const navigation = [
    { name: t('nav.dashboard'), href: "/demo/dashboard", icon: LayoutDashboard },
    { name: t('nav.players'), href: "/demo/players", icon: Users },
    { name: t('nav.matches'), href: "/demo/matches", icon: Calendar },
  ];

  // Check if current path is active
  const isActive = (href: string) => location.pathname === href;

  // Desktop navigation links
  const renderDesktopNavLinks = () => (
    navigation.map((item) => (
      <Link
        key={item.name}
        to={item.href}
        className={cn(
          "px-4 py-2 rounded-md text-sm font-medium flex items-center gap-2 transition-all duration-200",
          isActive(item.href)
            ? "bg-white/10 text-white"
            : "text-white/70 hover:bg-white/5 hover:text-white"
        )}
      >
        <item.icon className="w-4 h-4" />
        <span className="hidden lg:inline">{item.name}</span>
      </Link>
    ))
  );

  // Mobile navigation links
  const renderMobileNavLinks = () => (
    navigation.map((item) => (
      <Link
        key={item.name}
        to={item.href}
        className={cn(
          "px-3 py-3 rounded-md text-sm font-medium flex items-center gap-3 transition-colors duration-200",
          isActive(item.href)
            ? "bg-white/10 text-white"
            : "text-white/70 hover:bg-white/5 hover:text-white"
        )}
        onClick={() => setIsMobileMenuOpen(false)}
      >
        <item.icon className="w-5 h-5" />
        <span>{item.name}</span>
      </Link>
    ))
  );

  // Mobile bottom navigation - matching authenticated app structure
  const renderMobileBottomNav = () => {
    // Use same navigation items as authenticated app for consistency
    // Note: Leaderboard is accessed through dashboard tabs, not as a separate page
    const bottomNavItems = [
      { name: t('nav.dashboard'), href: "/demo/dashboard", icon: LayoutDashboard },
      { name: t('nav.players'), href: "/demo/players", icon: Users },
      { name: t('nav.matches'), href: "/demo/matches", icon: Calendar },
      { name: t('demo.info', 'Demo Info'), href: "/demo/dashboard", icon: Trophy }, // Link back to dashboard
    ];

    return (
      <div className="md:hidden fixed bottom-0 left-0 right-0 bg-soccer-primary dark:bg-soccer-primary-dark text-white dark:text-white z-50 border-t border-white/10 dark:border-white/10 shadow-lg backdrop-blur-sm animate-slide-in-up">
        <div className="grid grid-cols-4 h-16">
          {bottomNavItems.map((item, index) => (
            <Link
              key={item.name}
              to={item.href}
              className={cn(
                "flex flex-col items-center justify-center px-2 py-1 nav-item relative group",
                isActive(item.href)
                  ? "bg-white/20 text-white dark:text-white shadow-md active"
                  : "text-white/80 dark:text-white/80 hover:bg-white/10 hover:text-white dark:hover:text-white"
              )}
              style={{
                animationDelay: `${index * 50}ms`
              }}
            >
              <item.icon className={cn(
                "h-5 w-5 mb-1 animate-smooth",
                isActive(item.href)
                  ? "scale-110"
                  : "group-hover:scale-105"
              )} />
              <span className={cn(
                "text-xs font-medium animate-smooth",
                isActive(item.href)
                  ? "font-semibold"
                  : ""
              )}>{item.name}</span>
              {isActive(item.href) && (
                <div className="absolute top-0 left-1/2 transform -translate-x-1/2 w-10 h-1 bg-white dark:bg-white rounded-full shadow-sm animate-scale-in" />
              )}
            </Link>
          ))}
        </div>
      </div>
    );
  };

  return (
    <TooltipProvider>
      <div className="min-h-screen flex flex-col bg-background text-foreground transition-all duration-300 page-transition relative">
        <HalftoneBackground className="halftone-background" />
        
        {/* Demo Banner */}
        <div className="bg-black dark:bg-white text-white dark:text-black py-2 px-4 text-center relative z-10">
          <div className="flex items-center justify-center gap-2 text-sm font-medium">
            <Play className="h-4 w-4" />
            <span>{t('demo.banner', 'You are viewing the Fulbito Stats Demo')}</span>
            <Tooltip>
              <TooltipTrigger>
                <Info className="h-4 w-4 cursor-help" />
              </TooltipTrigger>
              <TooltipContent>
                <p className="max-w-xs text-center">
                  {t('demo.bannerTooltip', 'This is a demonstration with sample data. Sign up to create your own group and track real matches!')}
                </p>
              </TooltipContent>
            </Tooltip>
          </div>
        </div>

        {/* Top Navigation */}
        <nav className="bg-soccer-primary dark:bg-soccer-primary-dark text-white relative z-10">
          <div className="container mx-auto">
            <div className="flex items-center justify-between h-16">
              {/* Left section: Logo and Demo indicator */}
              <div className="flex items-center gap-4">
                <div className="h-8 w-8">
                  <LogoComponent to="/demo/dashboard" height="32px" width="32px" className="text-white" />
                </div>
                <div className="bg-white/10 px-3 py-1 rounded text-sm flex items-center">
                  <Play className="h-3.5 w-3.5 mr-2" />
                  {t('demo.mode', 'Demo Mode')}
                </div>
              </div>

              {/* Center section: Navigation Links (Desktop) */}
              <div className="hidden md:flex items-center gap-1 flex-1 justify-center">
                {renderDesktopNavLinks()}
              </div>

              {/* Right section: Controls and Back to Landing */}
              <div className="flex items-center gap-2">
                <div className="hidden md:flex items-center gap-2">
                  <ThemeToggle />
                  <LanguageToggle />
                  <Button
                    asChild
                    variant="ghost"
                    size="sm"
                    className="text-white hover:bg-white/10"
                  >
                    <Link to="/">
                      <ArrowLeft className="h-4 w-4 mr-2" />
                      {t('demo.backToLanding', 'Back to Landing')}
                    </Link>
                  </Button>
                </div>

                {/* Mobile menu trigger */}
                <div className="md:hidden">
                  <Sheet open={isMobileMenuOpen} onOpenChange={setIsMobileMenuOpen}>
                    <SheetTrigger asChild>
                      <Button variant="ghost" size="icon" className="hover:bg-white/10 focus:bg-white/10 text-white">
                        <Menu className="h-6 w-6" />
                        <span className="sr-only">Open menu</span>
                      </Button>
                    </SheetTrigger>
                    <SheetContent side="left" className="bg-soccer-primary dark:bg-soccer-primary-dark text-white border-soccer-accent/20 pt-10 w-[280px] transition-colors duration-200">
                      <SheetHeader className="mb-4 text-left">
                        <SheetTitle className="text-white flex items-center gap-2">
                          <LogoComponent to="/demo/dashboard" height="24px" width="24px" />
                          <span>{t('demo.mode', 'Demo Mode')}</span>
                        </SheetTitle>
                      </SheetHeader>
                      
                      <div className="flex flex-col space-y-2 mb-6">
                        {renderMobileNavLinks()}
                      </div>

                      <div className="border-t border-white/10 pt-4 space-y-3">
                        <div className="flex items-center justify-between">
                          <span className="text-sm text-white/70">{t('settings.theme')}</span>
                          <ThemeToggle />
                        </div>
                        <div className="flex items-center justify-between">
                          <span className="text-sm text-white/70">{t('settings.language')}</span>
                          <LanguageToggle />
                        </div>
                        <Button
                          asChild
                          variant="ghost"
                          className="w-full justify-start text-white hover:bg-white/10"
                        >
                          <Link to="/" onClick={() => setIsMobileMenuOpen(false)}>
                            <ArrowLeft className="h-4 w-4 mr-2" />
                            {t('demo.backToLanding', 'Back to Landing')}
                          </Link>
                        </Button>
                      </div>
                    </SheetContent>
                  </Sheet>
                </div>
              </div>
            </div>
          </div>
        </nav>

        {/* Main Content */}
        <main className={`flex-1 w-full ${isMobile ? 'pb-16' : ''} relative z-10`}>
          <div className="md:py-8 md:px-8 p-4 space-y-8 transition-all duration-300 max-w-[1400px] mx-auto animate-fade-in">
            {title ? (
              <>
                <div className="flex justify-between items-center mb-6">
                  <h1 className="text-2xl md:text-3xl font-bold tracking-tight text-foreground bg-gradient-to-r from-soccer-primary to-soccer-primary-light bg-clip-text text-transparent hover:scale-105 transition-transform duration-300">{title}</h1>
                </div>
                <div className="w-full space-y-6">{children}</div>
              </>
            ) : (
              <div className="space-y-6">{children}</div>
            )}
          </div>
        </main>

        {/* Mobile Bottom Navigation */}
        {renderMobileBottomNav()}

        {/* Footer */}
        <footer className="bg-gray-100 dark:bg-gray-800 py-4 border-t dark:border-gray-700 relative z-10">
          <div className="container mx-auto text-center text-sm text-gray-500 dark:text-gray-400">
            <p className="mb-2">
              {t('demo.footerText', 'This is a demonstration with sample data.')} {' '}
              <Link to="/signup" className="text-soccer-primary hover:underline">
                {t('demo.signUpToStart', 'Sign up to get started with your own group!')}
              </Link>
            </p>
            <div className="flex justify-center gap-4">
              <a
                href={FEEDBACK_FORM_URL}
                target="_blank"
                rel="noopener noreferrer"
                className="text-purple-600 hover:text-purple-700 dark:text-purple-400 dark:hover:text-purple-300 flex items-center gap-1 hover:underline"
              >
                <MessageCircle className="h-4 w-4" />
                {t('footer.feedback')}
              </a>
              <a
                href={CAFECITO_URL}
                target="_blank"
                rel="noopener noreferrer"
                className="text-blue-600 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300 flex items-center gap-1 hover:underline"
              >
                <Coffee className="h-4 w-4" />
                {t('footer.donate')}
              </a>
            </div>
          </div>
        </footer>
      </div>
    </TooltipProvider>
  );
};

export default DemoLayout;
