import { useState, useMemo, useEffect } from "react";
import { useTranslation } from "react-i18next";
import DemoLayout from "@/components/layout/DemoLayout";
import { useDemo } from "@/context/DemoContext";
import StatCard from "@/components/StatCard";
import {
  Users, Calendar, TrendingUp, Percent, User, ShieldCheck, Trophy, History, Sparkles, Search, LayoutDashboard, Handshake, Shuffle,
  Crown, Medal, UserPlus, CalendarPlus, UsersRound, Flame, ArrowRight
} from "lucide-react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { useMediaQuery } from "@/hooks/use-media-query";
import { useToast } from "@/hooks/use-toast";
import { format } from "date-fns";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { EnhancedCard } from "@/components/ui/enhanced-card";
import { AnimatedButton } from "@/components/ui/animated-button";
import { Slider } from "@/components/ui/slider";
import { cn } from "@/lib/utils";
import DashboardLeaderboard from "@/components/dashboard/DashboardLeaderboard";
import DashboardChemistry from "@/components/dashboard/DashboardChemistry";
import DashboardTeamGenerator from "@/components/dashboard/DashboardTeamGenerator";

const DemoDashboardPage = () => {
  const { t } = useTranslation();
  const { players, matches, group } = useDemo();
  const { toast } = useToast();
  const [selectedPlayerId, setSelectedPlayerId] = useState<string>("");
  const [minGamesForLeaderboard, setMinGamesForLeaderboard] = useState<string>("3");
  const [minGamesForChemistry, setMinGamesForChemistry] = useState<string>("3");
  const [activeTab, setActiveTab] = useState("overview");
  const isMobile = useMediaQuery("(max-width: 768px)");

  // Set page title
  useEffect(() => {
    document.title = `${t('nav.dashboard')} - ${t('app.name')} Demo`;
  }, [t]);

  // Calculate player statistics
  const playerStatsWithWinRate = useMemo(() => {
    return players.map(player => {
      const playerMatches = matches.filter(match => 
        match.teama.includes(player.id) || match.teamb.includes(player.id)
      );

      let wins = 0;
      let draws = 0;
      let losses = 0;

      playerMatches.forEach(match => {
        const isTeamA = match.teama.includes(player.id);
        
        if (match.winner === 'Draw') {
          draws++;
        } else if (
          (isTeamA && match.winner === 'A') || 
          (!isTeamA && match.winner === 'B')
        ) {
          wins++;
        } else {
          losses++;
        }
      });

      const played = playerMatches.length;
      const winRate = played > 0 ? (wins / played) * 100 : 0;

      return {
        ...player,
        played,
        wins,
        draws,
        losses,
        winRate,
      };
    });
  }, [players, matches]);

  // Get leaderboard leader
  const leaderboardLeader = useMemo(() => {
    const minGames = parseInt(minGamesForLeaderboard, 10);
    const eligiblePlayers = playerStatsWithWinRate
      .filter(p => p.played >= minGames)
      .sort((a, b) => b.winRate - a.winRate);
    
    return eligiblePlayers[0] || null;
  }, [playerStatsWithWinRate, minGamesForLeaderboard]);

  // Calculate group stats
  const groupStats = useMemo(() => {
    const totalMatches = matches.length;
    const totalPlayers = players.length;
    const totalGoals = matches.reduce((sum, match) => 
      sum + (match.scorea || 0) + (match.scoreb || 0), 0
    );
    const avgGoalsPerMatch = totalMatches > 0 ? (totalGoals / totalMatches).toFixed(1) : '0';

    return {
      totalPlayers,
      totalMatches,
      totalGoals,
      avgGoalsPerMatch,
    };
  }, [players, matches]);

  // Get recent matches (last 3)
  const recentMatches = useMemo(() => {
    return matches.slice(0, 3);
  }, [matches]);

  // Get selected player data
  const selectedPlayerData = useMemo(() => {
    if (!selectedPlayerId) return null;
    const playerId = parseInt(selectedPlayerId);
    return playerStatsWithWinRate.find(p => p.id === playerId) || null;
  }, [selectedPlayerId, playerStatsWithWinRate]);

  // Transform demo data for dashboard components
  const transformedMatches = useMemo(() => {
    return matches.map(match => ({
      ...match,
      date: new Date(match.match_date),
      teamA: match.teama,
      teamB: match.teamb,
      scoreA: match.scorea,
      scoreB: match.scoreb,
    }));
  }, [matches]);

  // Calculate max games played for slider
  const maxGamesPlayed = useMemo(() => {
    return Math.max(...playerStatsWithWinRate.map(p => p.played), 5);
  }, [playerStatsWithWinRate]);

  // Get oldest match for "Playing Since" display
  const oldestMatch = useMemo(() => {
    if (matches.length === 0) return null;
    return matches.reduce((oldest, match) => {
      const matchDate = new Date(match.match_date);
      const oldestDate = new Date(oldest.match_date);
      return matchDate < oldestDate ? match : oldest;
    });
  }, [matches]);

  // Get top leaderboard player (same as leaderboardLeader but with different filtering)
  const topLeaderboardPlayer = useMemo(() => {
    const minGames = parseInt(minGamesForLeaderboard, 10);
    const eligiblePlayers = playerStatsWithWinRate
      .filter(p => p.played >= minGames)
      .sort((a, b) => {
        // Sort by win rate first, then by games played as tiebreaker
        if (b.winRate !== a.winRate) return b.winRate - a.winRate;
        return b.played - a.played;
      });

    if (eligiblePlayers.length === 0) return null;

    const topPlayer = eligiblePlayers[0];
    return {
      ...topPlayer,
      avgRating: Math.round((topPlayer.skills + topPlayer.effort + topPlayer.stamina) / 3)
    };
  }, [playerStatsWithWinRate, minGamesForLeaderboard]);

  // Helper function to get player name
  const getPlayerName = (id: number): string => {
    const player = players.find((p) => p.id === id);
    return player ? player.name : `Player ${id}`;
  };

  // Calculate chemistry data
  const chemistryData = useMemo(() => {
    const minGames = parseInt(minGamesForChemistry, 10);
    const duosMap = new Map<string, { played: number; wins: number; players: number[] }>();

    matches.forEach(match => {
      const processTeam = (team: number[], won: boolean) => {
        for (let i = 0; i < team.length; i++) {
          for (let j = i + 1; j < team.length; j++) {
            const key = [team[i], team[j]].sort().join('-');
            const existing = duosMap.get(key) || { played: 0, wins: 0, players: [team[i], team[j]].sort() };
            existing.played++;
            if (won) existing.wins++;
            duosMap.set(key, existing);
          }
        }
      };

      if (match.winner === 'A') {
        processTeam(match.teama, true);
        processTeam(match.teamb, false);
      } else if (match.winner === 'B') {
        processTeam(match.teama, false);
        processTeam(match.teamb, true);
      } else {
        // Draw - no wins for either team
        processTeam(match.teama, false);
        processTeam(match.teamb, false);
      }
    });

    const duos = Array.from(duosMap.values())
      .filter(duo => duo.played >= minGames)
      .map(duo => ({
        ...duo,
        winRate: duo.played > 0 ? (duo.wins / duo.played) * 100 : 0
      }))
      .sort((a, b) => b.winRate - a.winRate);

    return { duos: duos.slice(0, 3) }; // Top 3 duos
  }, [matches, minGamesForChemistry]);

  // Calculate best chemistry for the live app style display
  const bestChemistry = useMemo(() => {
    const minGames = parseInt(minGamesForChemistry, 10);
    const duosMap = new Map<string, { played: number; wins: number; players: number[] }>();
    const triosMap = new Map<string, { played: number; wins: number; players: number[] }>();
    const quadsMap = new Map<string, { played: number; wins: number; players: number[] }>();

    matches.forEach(match => {
      const processTeam = (team: number[], won: boolean) => {
        // Process duos
        for (let i = 0; i < team.length; i++) {
          for (let j = i + 1; j < team.length; j++) {
            const key = [team[i], team[j]].sort().join('-');
            const existing = duosMap.get(key) || { played: 0, wins: 0, players: [team[i], team[j]].sort() };
            existing.played++;
            if (won) existing.wins++;
            duosMap.set(key, existing);
          }
        }

        // Process trios
        for (let i = 0; i < team.length; i++) {
          for (let j = i + 1; j < team.length; j++) {
            for (let k = j + 1; k < team.length; k++) {
              const key = [team[i], team[j], team[k]].sort().join('-');
              const existing = triosMap.get(key) || { played: 0, wins: 0, players: [team[i], team[j], team[k]].sort() };
              existing.played++;
              if (won) existing.wins++;
              triosMap.set(key, existing);
            }
          }
        }

        // Process quads
        for (let i = 0; i < team.length; i++) {
          for (let j = i + 1; j < team.length; j++) {
            for (let k = j + 1; k < team.length; k++) {
              for (let l = k + 1; l < team.length; l++) {
                const key = [team[i], team[j], team[k], team[l]].sort().join('-');
                const existing = quadsMap.get(key) || { played: 0, wins: 0, players: [team[i], team[j], team[k], team[l]].sort() };
                existing.played++;
                if (won) existing.wins++;
                quadsMap.set(key, existing);
              }
            }
          }
        }
      };

      if (match.winner === 'A') {
        processTeam(match.teama, true);
        processTeam(match.teamb, false);
      } else if (match.winner === 'B') {
        processTeam(match.teama, false);
        processTeam(match.teamb, true);
      } else {
        // Draw - no wins for either team
        processTeam(match.teama, false);
        processTeam(match.teamb, false);
      }
    });

    const duos = Array.from(duosMap.values())
      .filter(duo => duo.played >= minGames)
      .map(duo => ({
        ...duo,
        winRate: duo.played > 0 ? (duo.wins / duo.played) * 100 : 0
      }))
      .sort((a, b) => b.winRate - a.winRate);

    const trios = Array.from(triosMap.values())
      .filter(trio => trio.played >= minGames)
      .map(trio => ({
        ...trio,
        winRate: trio.played > 0 ? (trio.wins / trio.played) * 100 : 0
      }))
      .sort((a, b) => b.winRate - a.winRate);

    const quads = Array.from(quadsMap.values())
      .filter(quad => quad.played >= minGames)
      .map(quad => ({
        ...quad,
        winRate: quad.played > 0 ? (quad.wins / quad.played) * 100 : 0
      }))
      .sort((a, b) => b.winRate - a.winRate);

    return {
      duos: duos.slice(0, 1),
      trios: trios.slice(0, 1),
      quads: quads.slice(0, 1)
    };
  }, [matches, minGamesForChemistry]);

  return (
    <DemoLayout>
      <div className="space-y-6">
        {/* Demo Info Card */}
        <Card className="border-soccer-primary/20 bg-soccer-primary/5">
          <CardContent className="pt-6">
            <div className="flex items-start gap-3">
              <div className="h-8 w-8 rounded-full bg-soccer-primary/10 flex items-center justify-center flex-shrink-0">
                <Sparkles className="h-4 w-4 text-soccer-primary" />
              </div>
              <div className="flex-1">
                <h3 className="font-semibold text-soccer-primary mb-2">
                  {t('demo.welcomeTitle', 'Welcome to the Fulbito Stats Demo!')}
                </h3>
                <p className="text-sm text-muted-foreground mb-3">
                  {t('demo.welcomeDescription', 'Explore all features with realistic sample data. This demo includes 15 players and 30 matches to showcase the full capabilities of Fulbito Stats.')}
                </p>
                <div className="flex flex-wrap gap-2">
                  <Badge variant="outline" className="text-xs">
                    {t('demo.feature1', '15 Sample Players')}
                  </Badge>
                  <Badge variant="outline" className="text-xs">
                    {t('demo.feature2', '30 Match History')}
                  </Badge>
                  <Badge variant="outline" className="text-xs">
                    {t('demo.feature3', 'Live Statistics')}
                  </Badge>
                  <Badge variant="outline" className="text-xs">
                    {t('demo.feature4', 'Team Generator')}
                  </Badge>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Tab Navigation - matching authenticated dashboard */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid grid-cols-4 mb-6">
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <TabsTrigger
                    value="overview"
                    className={cn(
                      "dashboard-tab",
                      activeTab === "overview" && "bg-soccer-primary/10 text-white border border-soccer-primary/20"
                    )}
                  >
                    <LayoutDashboard className="h-4 w-4 md:mr-2" />
                    <span className="hidden md:inline">{t('dashboard.overview')}</span>
                  </TabsTrigger>
                </TooltipTrigger>
                <TooltipContent className="md:hidden">
                  <p>{t('dashboard.overview')}</p>
                </TooltipContent>
              </Tooltip>

              <Tooltip>
                <TooltipTrigger asChild>
                  <TabsTrigger
                    value="leaderboard"
                    className={cn(
                      "dashboard-tab",
                      activeTab === "leaderboard" && "bg-soccer-primary/10 text-white border border-soccer-primary/20"
                    )}
                  >
                    <Trophy className="h-4 w-4 md:mr-2" />
                    <span className="hidden md:inline">{t('nav.leaderboard')}</span>
                  </TabsTrigger>
                </TooltipTrigger>
                <TooltipContent className="md:hidden">
                  <p>{t('nav.leaderboard')}</p>
                </TooltipContent>
              </Tooltip>

              <Tooltip>
                <TooltipTrigger asChild>
                  <TabsTrigger
                    value="chemistry"
                    className={cn(
                      "dashboard-tab",
                      activeTab === "chemistry" && "bg-soccer-primary/10 text-white border border-soccer-primary/20"
                    )}
                  >
                    <Handshake className="h-4 w-4 md:mr-2" />
                    <span className="hidden md:inline">{t('nav.chemistry')}</span>
                  </TabsTrigger>
                </TooltipTrigger>
                <TooltipContent className="md:hidden">
                  <p>{t('nav.chemistry')}</p>
                </TooltipContent>
              </Tooltip>

              <Tooltip>
                <TooltipTrigger asChild>
                  <TabsTrigger
                    value="team-generator"
                    className={cn(
                      "dashboard-tab",
                      activeTab === "team-generator" && "bg-soccer-primary/10 text-white border border-soccer-primary/20"
                    )}
                  >
                    <Shuffle className="h-4 w-4 md:mr-2" />
                    <span className="hidden md:inline">{t('nav.teamGenerator')}</span>
                  </TabsTrigger>
                </TooltipTrigger>
                <TooltipContent className="md:hidden">
                  <p>{t('nav.teamGenerator')}</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </TabsList>

          <TabsContent value="overview">
            <div className="flex justify-start mb-4">
              <div className="flex items-center gap-4 w-full max-w-md">
                <div className="text-sm text-muted-foreground whitespace-nowrap">
                  Min. Games: {minGamesForLeaderboard}
                </div>
                <div className="flex-1">
                  <Slider
                    value={[parseInt(minGamesForLeaderboard)]}
                    onValueChange={(value) => {
                      const newValue = value[0];
                      setMinGamesForLeaderboard(newValue.toString());
                      setMinGamesForChemistry(newValue.toString());
                    }}
                    max={maxGamesPlayed}
                    min={0}
                    step={1}
                    className="w-full"
                    aria-label={`Minimum games filter, current value: ${minGamesForLeaderboard}, maximum: ${maxGamesPlayed}`}
                  />
                </div>
              </div>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
              {/* Group Stats Card - Enhanced */}
              <EnhancedCard hoverable className="lg:col-span-1 group">
                <div className="p-6">
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center gap-3">
                      <Users className="h-5 w-5 text-soccer-primary" />
                      <div className="font-semibold text-lg text-foreground">{t('dashboard.groupStats')}</div>
                    </div>
                    <div className="flex gap-2">
                      <Button
                        size="icon"
                        variant="ghost"
                        className="h-8 w-8 rounded-full hover:bg-blue-500/10 hover:text-blue-600 hover:scale-105 transition-all duration-200 text-blue-500 dark:text-blue-400 dark:hover:text-blue-300"
                        onClick={() => toast({
                          title: t('demo.readOnlyMode'),
                          description: t('demo.addPlayerNotAvailable', 'Adding players is not available in demo mode. Sign up to manage your own players!'),
                          variant: 'default',
                        })}
                        title={t('players.addPlayer')}
                      >
                        <UserPlus className="h-4 w-4" />
                      </Button>
                      <Button
                        size="icon"
                        variant="ghost"
                        className="h-8 w-8 rounded-full hover:bg-blue-500/10 hover:text-blue-600 hover:scale-105 transition-all duration-200 text-blue-500 dark:text-blue-400 dark:hover:text-blue-300"
                        onClick={() => toast({
                          title: t('demo.readOnlyMode'),
                          description: t('demo.addMatchNotAvailable', 'Adding matches is not available in demo mode. Sign up to track your own matches!'),
                          variant: 'default',
                        })}
                        title={t('matches.addMatch')}
                      >
                        <CalendarPlus className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                  <div className="space-y-6">
                    <div className="flex items-center gap-4 p-3 rounded-xl bg-gradient-to-r from-soccer-primary/5 to-soccer-primary/10 border border-soccer-primary/20 hover:border-soccer-primary/30 transition-all duration-300">
                      <div className="bg-gradient-to-br from-soccer-primary to-soccer-primary-light p-3 rounded-xl shadow-sm">
                        <Users className="h-5 w-5 text-white" aria-hidden="true" />
                      </div>
                      <div className="flex-1">
                        <div className="text-2xl font-bold text-foreground">{players.length}</div>
                        <div className="text-sm font-medium text-muted-foreground">{t('dashboard.totalPlayers')}</div>
                      </div>
                    </div>

                    <div className="flex items-center gap-4 p-3 rounded-xl bg-gradient-to-r from-soccer-primary/5 to-soccer-primary/10 border border-soccer-primary/20 hover:border-soccer-primary/30 transition-all duration-300">
                      <div className="bg-gradient-to-br from-soccer-primary to-soccer-primary-light p-3 rounded-xl shadow-sm">
                        <Trophy className="h-5 w-5 text-white" aria-hidden="true" />
                      </div>
                      <div className="flex-1">
                        <div className="text-2xl font-bold text-foreground">{matches.length}</div>
                        <div className="text-sm font-medium text-muted-foreground">{t('dashboard.totalMatches')}</div>
                      </div>
                    </div>

                    {oldestMatch && (
                      <div className="text-xs text-muted-foreground p-3 rounded-lg bg-gradient-to-r from-muted/30 to-muted/50 border border-muted">
                        <div className="flex flex-col items-center text-center">
                          <span className="font-medium text-sm">Playing Since</span>
                          <span className="font-bold text-lg text-amber-600 dark:text-amber-400">
                            {format(new Date(oldestMatch.match_date), "MMM d, yyyy")}
                          </span>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </EnhancedCard>

              {/* Leaderboard Leader Card - Enhanced */}
              <EnhancedCard hoverable className="lg:col-span-1 group">
                <div className="p-6">
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center gap-3">
                      <Crown className="h-5 w-5 text-soccer-primary" />
                      <div className="font-semibold text-lg text-foreground">{t('dashboard.leader', 'Leader')}</div>
                    </div>
                    <Button
                      size="icon"
                      variant="ghost"
                      className="h-8 w-8 rounded-full hover:bg-blue-500/10 hover:text-blue-600 text-blue-500 dark:text-blue-400 dark:hover:text-blue-300"
                      onClick={() => setActiveTab("leaderboard")}
                      title={t('nav.leaderboard')}
                    >
                      <ArrowRight className="h-4 w-4" />
                    </Button>
                  </div>

                  {topLeaderboardPlayer ? (
                    <div className="space-y-6">
                      {/* Player Name and Avatar */}
                      <div className="text-center p-4 rounded-xl bg-gradient-to-r from-soccer-primary/5 to-soccer-primary/10 border border-soccer-primary/20">
                        <div className="flex items-center justify-center mb-3">
                          <div className="bg-gradient-to-br from-amber-500 to-orange-500 p-4 rounded-full shadow-lg">
                            <Crown className="h-6 w-6 text-white" aria-hidden="true" />
                          </div>
                        </div>
                        <div className="text-xl font-bold text-amber-600 dark:text-amber-400">{topLeaderboardPlayer.name}</div>
                        <div className="text-sm font-medium text-muted-foreground">{t('dashboard.currentLeader', 'Current Leader')}</div>
                      </div>

                      {/* Stats Grid */}
                      <div className="grid grid-cols-3 gap-3">
                        <div className="text-center p-3 rounded-lg bg-gradient-to-br from-muted/20 to-muted/30 border border-muted/40">
                          <Medal className="h-5 w-5 text-foreground mx-auto mb-1" />
                          <div className="text-lg font-bold text-foreground">{topLeaderboardPlayer.avgRating}</div>
                          <div className="text-xs text-muted-foreground">{t('players.rating')}</div>
                        </div>
                        <div className="text-center p-3 rounded-lg bg-gradient-to-br from-soccer-primary/5 to-soccer-primary/10 border border-soccer-primary/20">
                          <Percent className="h-5 w-5 text-soccer-primary mx-auto mb-1" />
                          <div className="text-lg font-bold text-soccer-primary">{topLeaderboardPlayer.winRate.toFixed(1)}%</div>
                          <div className="text-xs text-muted-foreground">{t('players.winRate')}</div>
                        </div>
                        <div className="text-center p-3 rounded-lg bg-gradient-to-br from-muted/20 to-muted/30 border border-muted/40">
                          <Calendar className="h-5 w-5 text-foreground mx-auto mb-1" />
                          <div className="text-lg font-bold text-foreground">{topLeaderboardPlayer.played}</div>
                          <div className="text-xs text-muted-foreground">{t('players.played')}</div>
                        </div>
                      </div>
                    </div>
                  ) : (
                    <div className="flex flex-col items-center justify-center py-8 space-y-4">
                      <div className="bg-gradient-to-br from-gray-100 to-gray-200 dark:from-gray-800 dark:to-gray-900 p-6 rounded-full">
                        <Crown className="h-8 w-8 text-muted-foreground opacity-50" />
                      </div>
                      <div className="text-center">
                        <p className="text-sm font-medium text-muted-foreground">{t('leaderboard.noPlayersFound')}</p>
                        <p className="text-xs text-muted-foreground mt-1">{t('dashboard.addPlayersToSeeLeader', 'Add players to see the leader')}</p>
                      </div>
                    </div>
                  )}
                </div>
              </EnhancedCard>

              {/* Chemistry Card - Enhanced */}
              <EnhancedCard hoverable className="lg:col-span-1 group">
                <div className="p-6">
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center gap-3">
                      <Handshake className="h-5 w-5 text-soccer-primary" />
                      <div className="font-semibold text-lg text-foreground">{t('chemistry.title', 'Chemistry')}</div>
                    </div>
                    <Button
                      size="icon"
                      variant="ghost"
                      className="h-8 w-8 rounded-full hover:bg-blue-500/10 hover:text-blue-600 text-blue-500 dark:text-blue-400 dark:hover:text-blue-300"
                      onClick={() => setActiveTab("chemistry")}
                      title={t('nav.chemistry')}
                    >
                      <ArrowRight className="h-4 w-4" />
                    </Button>
                  </div>

                  {bestChemistry.duos.length > 0 || bestChemistry.trios.length > 0 || bestChemistry.quads.length > 0 ? (
                    <div className="space-y-4">
                      {bestChemistry.duos.length > 0 && (
                        <div className="p-3 rounded-lg bg-gradient-to-r from-soccer-primary/5 to-soccer-primary/10 border border-soccer-primary/20 hover:border-soccer-primary/30 transition-all duration-300">
                          <div className="flex items-center gap-2 mb-2">
                            <UsersRound className="h-4 w-4 text-foreground" />
                            <span className="text-sm font-medium text-foreground">{t('chemistry.bestDuo', 'Best Duo')}</span>
                          </div>
                          <div className="text-xs text-amber-600 dark:text-amber-400">
                            {bestChemistry.duos[0].players.map(id => getPlayerName(id)).join(' + ')}
                          </div>
                          <div className="text-xs text-muted-foreground font-medium">
                            {bestChemistry.duos[0].winRate.toFixed(1)}% ({bestChemistry.duos[0].played} games)
                          </div>
                        </div>
                      )}

                      {bestChemistry.trios.length > 0 && (
                        <div className="p-3 rounded-lg bg-gradient-to-r from-soccer-primary/5 to-soccer-primary/10 border border-soccer-primary/20 hover:border-soccer-primary/30 transition-all duration-300">
                          <div className="flex items-center gap-2 mb-2">
                            <Users className="h-4 w-4 text-foreground" />
                            <span className="text-sm font-medium text-foreground">{t('chemistry.bestTrio', 'Best Trio')}</span>
                          </div>
                          <div className="text-xs text-amber-600 dark:text-amber-400">
                            {bestChemistry.trios[0].players.map(id => getPlayerName(id)).join(' + ')}
                          </div>
                          <div className="text-xs text-muted-foreground font-medium">
                            {bestChemistry.trios[0].winRate.toFixed(1)}% ({bestChemistry.trios[0].played} games)
                          </div>
                        </div>
                      )}

                      {bestChemistry.quads.length > 0 && (
                        <div className="p-3 rounded-lg bg-gradient-to-r from-soccer-primary/5 to-soccer-primary/10 border border-soccer-primary/20 hover:border-soccer-primary/30 transition-all duration-300">
                          <div className="flex items-center gap-2 mb-2">
                            <Users className="h-4 w-4 text-foreground" />
                            <span className="text-sm font-medium text-foreground">{t('chemistry.bestQuad', 'Best Quad')}</span>
                          </div>
                          <div className="text-xs text-amber-600 dark:text-amber-400">
                            {bestChemistry.quads[0].players.map(id => getPlayerName(id)).join(' + ')}
                          </div>
                          <div className="text-xs text-muted-foreground font-medium">
                            {bestChemistry.quads[0].winRate.toFixed(1)}% ({bestChemistry.quads[0].played} games)
                          </div>
                        </div>
                      )}
                    </div>
                  ) : (
                    <div className="flex flex-col items-center justify-center py-8 space-y-4">
                      <div className="bg-gradient-to-br from-gray-100 to-gray-200 dark:from-gray-800 dark:to-gray-900 p-6 rounded-full">
                        <Handshake className="h-8 w-8 text-muted-foreground opacity-50" />
                      </div>
                      <div className="text-center">
                        <p className="text-sm font-medium text-muted-foreground">{t('chemistry.noData', 'No chemistry data available')}</p>
                        <p className="text-xs text-muted-foreground mt-1">{t('chemistry.playMoreMatches', 'Play more matches to see chemistry')}</p>
                      </div>
                    </div>
                  )}
                </div>
              </EnhancedCard>
            </div>

            {/* Player Snapshot Section */}
            <EnhancedCard hoverable className="mt-8">
              <EnhancedCard.Header>
                <EnhancedCard.Title className="flex items-center gap-2 text-foreground">
                  <User className="h-5 w-5 text-soccer-primary" />
                  {t('dashboard.playerSnapshot', 'Player Snapshot')}
                </EnhancedCard.Title>
              </EnhancedCard.Header>
              <EnhancedCard.Content>
                <div className="flex items-center gap-4 mb-6">
                  <div className="flex items-center gap-2">
                    <label htmlFor="player-select" className="text-sm font-medium">
                      {t('dashboard.selectPlayer', 'Select Player')}:
                    </label>
                    <Select value={selectedPlayerId} onValueChange={setSelectedPlayerId}>
                      <SelectTrigger className="w-[200px]" id="player-select">
                        <SelectValue placeholder={t('dashboard.choosePlayer', 'Choose a player')} />
                      </SelectTrigger>
                      <SelectContent>
                        {playerStatsWithWinRate
                          .sort((a, b) => b.played - a.played)
                          .map((player) => (
                            <SelectItem key={player.id.toString()} value={player.id.toString()}>
                              {player.name} ({player.played} {t('players.matches', 'matches')})
                            </SelectItem>
                          ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                {selectedPlayerData && (
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                    {/* Basic Stats */}
                    <div className="space-y-4">
                      <h4 className="font-semibold text-lg flex items-center gap-2">
                        <Trophy className="h-4 w-4 text-soccer-primary" />
                        {t('dashboard.basicStats', 'Basic Stats')}
                      </h4>
                      <div className="grid grid-cols-2 gap-3">
                        <div className="p-3 rounded-lg bg-gradient-to-br from-muted/20 to-muted/30 border border-muted/40">
                          <div className="text-lg font-bold text-foreground">{selectedPlayerData.played}</div>
                          <div className="text-xs text-muted-foreground">{t('players.played')}</div>
                        </div>
                        <div className="p-3 rounded-lg bg-gradient-to-br from-green-50 to-green-100 dark:from-green-900/20 dark:to-green-800/20 border border-green-200 dark:border-green-800">
                          <div className="text-lg font-bold text-green-700 dark:text-green-400">{selectedPlayerData.wins}</div>
                          <div className="text-xs text-green-600 dark:text-green-500">{t('players.wins')}</div>
                        </div>
                        <div className="p-3 rounded-lg bg-gradient-to-br from-yellow-50 to-yellow-100 dark:from-yellow-900/20 dark:to-yellow-800/20 border border-yellow-200 dark:border-yellow-800">
                          <div className="text-lg font-bold text-yellow-700 dark:text-yellow-400">{selectedPlayerData.draws}</div>
                          <div className="text-xs text-yellow-600 dark:text-yellow-500">{t('players.draws')}</div>
                        </div>
                        <div className="p-3 rounded-lg bg-gradient-to-br from-red-50 to-red-100 dark:from-red-900/20 dark:to-red-800/20 border border-red-200 dark:border-red-800">
                          <div className="text-lg font-bold text-red-700 dark:text-red-400">{selectedPlayerData.losses}</div>
                          <div className="text-xs text-red-600 dark:text-red-500">{t('players.losses')}</div>
                        </div>
                      </div>
                    </div>

                    {/* Win Rate */}
                    <div className="space-y-4">
                      <h4 className="font-semibold text-lg flex items-center gap-2">
                        <Percent className="h-4 w-4 text-soccer-primary" />
                        {t('dashboard.performance', 'Performance')}
                      </h4>
                      <div className="p-4 rounded-lg bg-gradient-to-br from-soccer-primary/5 to-soccer-primary/10 border border-soccer-primary/20">
                        <div className="text-center">
                          <div className="text-3xl font-bold text-soccer-primary">{selectedPlayerData.winRate.toFixed(1)}%</div>
                          <div className="text-sm text-muted-foreground">{t('players.winRate')}</div>
                        </div>
                      </div>
                    </div>

                    {/* Player Attributes */}
                    <div className="space-y-4">
                      <h4 className="font-semibold text-lg flex items-center gap-2">
                        <User className="h-4 w-4 text-soccer-primary" />
                        {t('dashboard.attributes', 'Attributes')}
                      </h4>
                      <div className="space-y-3">
                        <div className="p-2 rounded bg-gradient-to-br from-muted/20 to-muted/30 border border-muted/40">
                          <div className="text-xs font-medium text-foreground mb-1">{t('players.skills')}</div>
                          <div className="text-sm font-bold text-foreground">{selectedPlayerData.skills}/10</div>
                        </div>
                        <div className="p-2 rounded bg-gradient-to-br from-muted/20 to-muted/30 border border-muted/40">
                          <div className="text-xs font-medium text-foreground mb-1">{t('players.effort')}</div>
                          <div className="text-sm font-bold text-foreground">{selectedPlayerData.effort}/10</div>
                        </div>
                        <div className="p-2 rounded bg-gradient-to-br from-muted/20 to-muted/30 border border-muted/40">
                          <div className="text-xs font-medium text-foreground mb-1">{t('players.stamina')}</div>
                          <div className="text-sm font-bold text-foreground">{selectedPlayerData.stamina}/10</div>
                        </div>
                      </div>
                    </div>

                    {/* Demo Note */}
                    <div className="space-y-4">
                      <h4 className="font-semibold text-lg flex items-center gap-2">
                        <Sparkles className="h-4 w-4 text-soccer-primary" />
                        {t('demo.note', 'Demo Note')}
                      </h4>
                      <div className="p-3 rounded-lg bg-gradient-to-r from-soccer-primary/5 to-soccer-primary/10 border border-soccer-primary/20">
                        <p className="text-xs text-muted-foreground">
                          {t('demo.playerSnapshotNote', 'In the full version, this section includes recent form, chemistry highlights, and World Cup Run tracking.')}
                        </p>
                      </div>
                    </div>
                  </div>
                )}

                {!selectedPlayerId && (
                  <div className="text-center py-8">
                    <User className="h-12 w-12 mx-auto mb-4 text-muted-foreground opacity-50" />
                    <p className="text-muted-foreground">{t('dashboard.selectPlayerToView', 'Select a player to view their snapshot')}</p>
                  </div>
                )}
              </EnhancedCard.Content>
            </EnhancedCard>
          </TabsContent>

          <TabsContent value="leaderboard">
            <DashboardLeaderboard players={players} matches={transformedMatches} />
          </TabsContent>

          <TabsContent value="chemistry">
            <DashboardChemistry players={players} matches={transformedMatches} />
          </TabsContent>

          <TabsContent value="team-generator">
            <DashboardTeamGenerator
              players={playerStatsWithWinRate}
              matches={transformedMatches}
              onSaveMatch={async (newMatch) => {
                // Demo mode - show toast that saving is not available
                toast({
                  title: t('demo.readOnlyMode', 'Demo Mode'),
                  description: t('demo.saveMatchNotAvailable', 'Saving matches is not available in demo mode. Sign up to track your own matches!'),
                  variant: "default"
                });
              }}
            />
          </TabsContent>
        </Tabs>
      </div>
    </DemoLayout>
  );
};

export default DemoDashboardPage;
