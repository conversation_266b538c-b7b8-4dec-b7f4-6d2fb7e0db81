import { useState, useEffect, useMemo } from "react";
import { useTranslation } from "react-i18next";
import { But<PERSON> } from "@/components/ui/button";
import DemoLayout from "@/components/layout/DemoLayout";
import { useDemo } from "@/context/DemoContext";
import { EmptyState } from "@/components/ui/empty-state";
import { MobileTable } from "@/components/ui/mobile-table";
import { useMediaQuery } from "@/hooks/use-media-query";
import { useToast } from "@/hooks/use-toast";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Plus, Calendar, Filter, CalendarPlus, X, Loader2, Edit, Trash2 } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { format } from "date-fns";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { <PERSON>, CardContent, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { MatchFiltersComponent, MatchFilters as MatchFiltersType } from "@/components/matches/MatchFilters";

// Enhanced components
import { EnhancedCard } from "@/components/ui/enhanced-card";
import { ResponsiveTable } from "@/components/ui/responsive-table";
import { AnimatedButton } from "@/components/ui/animated-button";
import { EnhancedInput } from "@/components/ui/enhanced-input";

const DemoMatchesPage = () => {
  const { t } = useTranslation();
  const { players, matches } = useDemo();
  const [loading, setLoading] = useState(false);
  const isMobile = useMediaQuery("(max-width: 768px)");
  const { toast } = useToast();

  // Filter states
  const [filters, setFilters] = useState<MatchFiltersType>({
    search: "",
    dateRange: { from: undefined, to: undefined },
    players: [],
    resultFilter: 'all',
    sortBy: 'date-desc'
  });

  // Set page title
  useEffect(() => {
    document.title = `${t('nav.matches')} - ${t('app.name')} Demo`;
  }, [t]);

  // Get player name helper
  const getPlayerName = (playerId: number) => {
    const player = players.find(p => p.id === playerId);
    return player?.name || `Player ${playerId}`;
  };

  // Filter and sort matches
  const filteredMatches = useMemo(() => {
    let filtered = [...matches];

    // Apply search filter
    if (filters.search) {
      filtered = filtered.filter(match => {
        const allPlayerNames = [...match.teama, ...match.teamb]
          .map(id => getPlayerName(id))
          .join(' ')
          .toLowerCase();
        return allPlayerNames.includes(filters.search.toLowerCase());
      });
    }

    // Apply date range filter
    if (filters.dateRange.from || filters.dateRange.to) {
      filtered = filtered.filter(match => {
        const matchDate = new Date(match.match_date);
        if (filters.dateRange.from && matchDate < filters.dateRange.from) return false;
        if (filters.dateRange.to && matchDate > filters.dateRange.to) return false;
        return true;
      });
    }

    // Apply players filter
    if (filters.players.length > 0) {
      filtered = filtered.filter(match => {
        const allPlayers = [...match.teama, ...match.teamb];
        return filters.players.some(playerId => allPlayers.includes(playerId));
      });
    }

    // Apply result filter
    if (filters.resultFilter !== "all") {
      filtered = filtered.filter(match => {
        if (filters.resultFilter === "wins") return match.winner !== 'Draw';
        if (filters.resultFilter === "draws") return match.winner === 'Draw';
        if (filters.resultFilter === "losses") return match.winner !== 'Draw'; // This is a bit ambiguous in demo
        return true;
      });
    }

    // Apply sorting
    filtered.sort((a, b) => {
      const dateA = new Date(a.match_date).getTime();
      const dateB = new Date(b.match_date).getTime();

      switch (filters.sortBy) {
        case "date-asc":
          return dateA - dateB;
        case "date-desc":
        default:
          return dateB - dateA;
      }
    });

    return filtered;
  }, [matches, filters, getPlayerName]);

  // Group matches by month/year for accordion display
  const groupedMatches = useMemo(() => {
    const groups = new Map<string, typeof filteredMatches>();

    filteredMatches.forEach(match => {
      const date = new Date(match.match_date);
      const monthYear = format(date, 'MMMM yyyy');

      if (!groups.has(monthYear)) {
        groups.set(monthYear, []);
      }
      groups.get(monthYear)!.push(match);
    });

    return Array.from(groups.entries()).sort((a, b) => {
      const dateA = new Date(a[1][0].match_date);
      const dateB = new Date(b[1][0].match_date);
      return dateB.getTime() - dateA.getTime(); // Most recent first
    });
  }, [filteredMatches]);

  const handleAddMatch = () => {
    toast({
      title: t('demo.readOnlyMode'),
      description: t('demo.addMatchNotAvailable', 'Adding matches is not available in demo mode. Sign up to track your own matches!'),
      variant: 'default',
    });
  };

  const handleEditMatch = () => {
    toast({
      title: t('demo.readOnlyMode'),
      description: t('demo.editMatchNotAvailable', 'Editing matches is not available in demo mode. Sign up to manage your own matches!'),
      variant: 'default',
    });
  };

  const handleDeleteMatch = () => {
    toast({
      title: t('demo.readOnlyMode'),
      description: t('demo.deleteMatchNotAvailable', 'Deleting matches is not available in demo mode. Sign up to manage your own matches!'),
      variant: 'default',
    });
  };

  // Get result badge
  const getResultBadge = (match: any) => {
    if (match.winner === 'Draw') {
      return <Badge variant="secondary">{t('matches.draw')}</Badge>;
    } else if (match.winner === 'A') {
      return <Badge variant="default" className="bg-green-600 hover:bg-green-700">{t('matches.teamAWin')}</Badge>;
    } else {
      return <Badge variant="default" className="bg-green-600 hover:bg-green-700">{t('matches.teamBWin')}</Badge>;
    }
  };

  // Desktop table columns
  const columns = [
    {
      header: t('matches.date'),
      accessorKey: 'match_date',
      cell: (match: any) => (
        <div className="font-medium">
          {format(new Date(match.match_date), 'MMM dd, yyyy')}
        </div>
      ),
    },
    {
      header: t('matches.teams'),
      accessorKey: 'teams',
      cell: (match: any) => (
        <div className="space-y-1">
          <div className="text-sm">
            <span className="font-medium">Team A:</span> {match.teama.map(id => getPlayerName(id)).join(', ')}
          </div>
          <div className="text-sm">
            <span className="font-medium">Team B:</span> {match.teamb.map(id => getPlayerName(id)).join(', ')}
          </div>
        </div>
      ),
    },
    {
      header: t('matches.score'),
      accessorKey: 'score',
      cell: (match: any) => (
        <div className="text-center">
          <div className="font-bold text-lg">
            {match.scorea} - {match.scoreb}
          </div>
        </div>
      ),
    },
    {
      header: t('matches.result'),
      accessorKey: 'result',
      cell: (match: any) => getResultBadge(match),
    },
    {
      header: t('matches.format'),
      accessorKey: 'format',
      cell: (match: any) => (
        <Badge variant="outline">
          {match.teama.length}v{match.teamb.length}
        </Badge>
      ),
    },
  ];

  return (
    <DemoLayout>
      <div className="space-y-4">
        <div className="flex justify-between items-center">
          <h1 className="text-2xl font-bold">{t('matches.title')}</h1>
          <div>
            <Button onClick={handleAddMatch} className="bg-soccer-primary hover:bg-soccer-primary/90" disabled={loading}>
              <Plus className="mr-2 h-4 w-4" /> {t('matches.addMatch')}
            </Button>
          </div>
        </div>

        {/* Advanced Filtering */}
        <MatchFiltersComponent
          filters={filters}
          onFiltersChange={setFilters}
          players={players}
          onReset={() => setFilters({
            search: "",
            dateRange: { from: undefined, to: undefined },
            players: [],
            resultFilter: 'all',
            sortBy: 'date-desc'
          })}
        />

        {/* Match List */}
        {loading ? (
          <div className="flex items-center justify-center py-12" aria-live="polite" aria-busy="true">
            <Loader2 className="h-8 w-8 animate-spin text-primary" />
            <span className="sr-only">{t('matches.loadingMatches', 'Loading matches...')}</span>
          </div>
        ) : filteredMatches.length === 0 ? (
          filters.search || filters.dateRange.from || filters.dateRange.to || filters.players.length > 0 || filters.resultFilter !== 'all' ? (
            <EmptyState
              icon={Filter}
              title={t('matches.noMatchesMatchFilters', 'No Matches Match Your Filters')}
              description={t('matches.adjustFilters', 'Try adjusting your filter criteria to see more results.')}
              actionLabel={t('matches.clearFilters', 'Clear Filters')}
              actionIcon={X}
              onAction={() => setFilters({
                search: "",
                dateRange: { from: undefined, to: undefined },
                players: [],
                resultFilter: 'all',
                sortBy: 'date-desc'
              })}
              className="bg-card border-border py-8"
            />
          ) : (
            <EmptyState
              icon={CalendarPlus}
              title={t('matches.noMatchesRecorded', 'No Matches Recorded Yet')}
              description={t('matches.noMatchesDescription')}
              actionLabel={t('matches.addFirstMatch')}
              actionIcon={Plus}
              onAction={handleAddMatch}
              className="bg-card border-border py-12"
            />
          )
        ) : (
          <div className="space-y-4">
            {groupedMatches.map(([monthYear, monthMatches]) => (
            <div key={monthYear}>
              <h2 className="text-base font-semibold mb-2">{t(`common.months.${monthYear.split(' ')[0].toLowerCase()}`, monthYear.split(' ')[0])} {monthYear.split(' ')[1]}</h2>
              <Accordion type="single" collapsible className="space-y-2">
                {monthMatches.map((match) => (
                  <AccordionItem
                    key={match.id}
                    value={match.id.toString()}
                    className="border rounded-lg shadow-sm"
                  >
                    <AccordionTrigger className="px-4 hover:no-underline [&[data-state=open]>div]:bg-muted">
                      <div className="flex items-center justify-between w-full py-1">
                        <div className="flex items-center gap-3">
                          <Calendar className="h-4 w-4 text-muted-foreground" />
                          <span>{format(new Date(match.match_date), 'd MMM yyyy').replace(/^(\d+) ([A-Za-z]+)/, (_, day, month) => `${day} ${t(`common.months.${month.toLowerCase()}`, month)}`)}</span>
                        </div>
                        <div className="flex items-center gap-3">
                          {(match.scorea !== null && match.scoreb !== null) && (
                            <>
                              <span className="text-xl font-semibold">
                                {match.scorea} - {match.scoreb}
                              </span>
                              <Badge
                                variant={match.winner === 'Draw' ? 'secondary' : 'default'}
                                className="ml-1"
                              >
                                {match.winner === 'A' ? t('matches.teamAWon') :
                                 match.winner === 'B' ? t('matches.teamBWon') : t('matches.draw')}
                              </Badge>
                            </>
                          )}
                        </div>
                      </div>
                    </AccordionTrigger>
                    <AccordionContent className="px-4 pb-4">
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        {/* Team A */}
                        <div className="space-y-2">
                          <h4 className="font-medium text-sm flex items-center gap-2">
                            <span className="w-2 h-2 bg-blue-500 rounded-full"></span>
                            {t('matches.teamA')}
                          </h4>
                          <div className="flex flex-wrap gap-1">
                            {match.teama.map((playerId) => (
                              <Badge key={playerId} variant="outline" className="text-xs">
                                {getPlayerName(playerId)}
                              </Badge>
                            ))}
                          </div>
                        </div>

                        {/* Team B */}
                        <div className="space-y-2">
                          <h4 className="font-medium text-sm flex items-center gap-2">
                            <span className="w-2 h-2 bg-red-500 rounded-full"></span>
                            {t('matches.teamB')}
                          </h4>
                          <div className="flex flex-wrap gap-1">
                            {match.teamb.map((playerId) => (
                              <Badge key={playerId} variant="outline" className="text-xs">
                                {getPlayerName(playerId)}
                              </Badge>
                            ))}
                          </div>
                        </div>

                        {/* Match Details */}
                        <div className="md:col-span-2 pt-2 border-t">
                          <div className="flex items-center justify-between">
                            <div className="flex items-center gap-4 text-sm text-muted-foreground">
                              <span>{t('matches.format')}: {match.teama.length}v{match.teamb.length}</span>
                              {match.goalscorers && match.goalscorers.length > 0 && (
                                <span>{t('matches.goals')}: {match.goalscorers.length}</span>
                              )}
                            </div>
                            <div className="flex gap-2">
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={(e) => {
                                  e.stopPropagation();
                                  handleEditMatch();
                                }}
                                className="h-8 px-2"
                              >
                                <Edit className="h-3 w-3 mr-1" />
                                {t('common.edit')}
                              </Button>
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={(e) => {
                                  e.stopPropagation();
                                  handleDeleteMatch();
                                }}
                                className="h-8 px-2 text-red-600 hover:text-red-700"
                              >
                                <Trash2 className="h-3 w-3 mr-1" />
                                {t('common.delete')}
                              </Button>
                            </div>
                          </div>
                        </div>
                      </div>
                    </AccordionContent>
                  </AccordionItem>
                ))}
              </Accordion>
            </div>
            ))}
          </div>
        )}
      </div>
    </DemoLayout>
  );
};

export default DemoMatchesPage;
